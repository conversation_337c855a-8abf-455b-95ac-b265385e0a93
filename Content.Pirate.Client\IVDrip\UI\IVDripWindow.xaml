<DefaultWindow xmlns="https://spacestation14.io"
               xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
               xmlns:goob="clr-namespace:Content.Goobstation.UIKit.UserInterface.Controls;assembly=Content.Goobstation.UIKit"
               xmlns:controls="clr-namespace:Content.Client.UserInterface.Controls"
               xmlns:gfx="clr-namespace:Robust.Client.Graphics;assembly=Robust.Client"
               Resizable="False"
               Title="IV стійка">
    <BoxContainer Orientation="Horizontal">
        <goob:StaticSpriteView
            Name="IVSprite"/>

            <!-- The control pannel -->
        <BoxContainer>
            <!-- <TextureButton TexturePath=""></TextureButton> TODO: Replace with texrure button -->
            <Button Name="InjectValveButton" TextAlign="Center"></Button>

            <!-- Injection Amount Selector -->
            <BoxContainer Orientation="Vertical">
                <Label Text="Об'єм ін'єкції:"></Label>
                <FloatSpinBox Name="InjectionVolumeSelector"></FloatSpinBox>
            </BoxContainer>

        </BoxContainer>
    </BoxContainer>
</DefaultWindow>
