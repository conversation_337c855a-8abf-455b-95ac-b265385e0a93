using Content.Shared._Shitmed.Targeting;
using Content.Shared.Chemistry.Components.SolutionManager;
using Content.Shared.Chemistry.EntitySystems;
using Content.Shared.Damage;
using Content.Shared.DragDrop;
using Content.Shared.Popups;
using Robust.Shared.Containers;

namespace Content.Pirate.Shared.IVDrip;

public sealed partial class SharedIVDripSystem : EntitySystem
{
    [Dependency] private readonly SharedSolutionContainerSystem _solutionContainer = default!;
    [Dependency] private readonly SharedPopupSystem _popup = default!;
    [Dependency] private readonly DamageableSystem _damageable = default!;

    public override void Initialize()
    {
        base.Initialize();
        // Dragging and connecting
        SubscribeLocalEvent<IVDripComponent, CanDragEvent>(OnCanDrag);
        SubscribeLocalEvent<IVDripComponent, CanDropDraggedEvent>(OnCanDropDragged);
        SubscribeLocalEvent<SolutionContainerManagerComponent, CanDropTargetEvent>(OnCanDropTarget);

        SubscribeLocalEvent<IVDripComponent, DragDropDraggedEvent>(OnDragDrop);
        SubscribeLocalEvent<IVDripComponent, IVConnectedDoAfterEvent>(OnConnectedDoAfter);

        // ItemSlots handling
        SubscribeLocalEvent<IVDripComponent, EntInsertedIntoContainerMessage>(OnItemInserted);
        SubscribeLocalEvent<IVDripComponent, EntRemovedFromContainerMessage>(OnItemRemoved);

        //UI handling
        SubscribeLocalEvent<IVDripComponent, BoundUIOpenedEvent>(OnOpenUI);
    }

    public bool TryConnectIVBag(Entity<IVDripComponent> ent, EntityUid ivBag)
    {
        // Don't allow items other than IVs and other solution containers
        if (!TryComp<SolutionContainerManagerComponent>(ivBag, out var iVSolutionContainerComp))
        {
            _popup.PopupEntity(Loc.GetString("iv-drip-cannot-connect-wrong-item-message"), ent, ent);
            return false;
        }

        if (!_solutionContainer.TryGetSolution(new(ivBag, iVSolutionContainerComp), ent.Comp.IVSolutionName, out var iVSolutionEntity, out var iVSolution))
            return false;

        ent.Comp.IVBag = ivBag;
        ent.Comp.IVSolutionEntity = iVSolutionEntity;
        ent.Comp.IVSolution = iVSolution;
        ent.Comp.IVInserted = true;

        Dirty(ent);
        return true; // Connected successfully!
    }

    public bool TryDisconnectIVBag(Entity<IVDripComponent> ent, bool eject = true)
    {
        if (!ent.Comp.IVInserted)
            return true;

        ent.Comp.IVBag = null;
        ent.Comp.IVSolutionEntity = null;
        ent.Comp.IVSolution = null;
        ent.Comp.IVInserted = false;

        ent.Comp.Inject = false; // If IV is removed, stop injecting

        if (eject)
            _itemSlots.TryEject(ent, ent.Comp.IVBagSlot, null, out _);

        Dirty(ent);
        return true; // Disconnected successfully!
    }

    public bool TryConnectTarget(Entity<IVDripComponent> ent, EntityUid target)
    {
        if (!TryComp<SolutionContainerManagerComponent>(target, out var targetSolutionContainerComp))
            return false;

        if (!_solutionContainer.TryGetSolution(new(target, targetSolutionContainerComp), ent.Comp.TargetSolutionName, out var targetSolutionEntity, out var targetSolution))
            return false;

        ent.Comp.TargetEntity = target;
        ent.Comp.TargetSolutionEntity = targetSolutionEntity;
        ent.Comp.TargetSolution = targetSolution;

        ent.Comp.Connected = true;

        Dirty(ent);
        return true; // Connected successfully!
    }

    /// <summary>
    /// Disconnects the target from the IV drip. Optionally deals damage to the target.
    /// </summary>
    /// <param name="ent">The IV target was disconnected from.</param>
    /// <param name="damage">If true, <see cref="IVDripComponent.RipOutDamage"/> is dealt to the target.</param>
    /// <returns>True if succesfully disconnected</returns>
    public bool TryDisconnectTarget(Entity<IVDripComponent> ent, bool damage = false)
    {
        if (!ent.Comp.Connected)
            return true;

        // If IV was disconnected via ripping of, deal some damage.
        if (damage)
        {
            _damageable.TryChangeDamage(ent.Comp.TargetEntity, ent.Comp.RipOutDamage,
                                        ignoreResistances: true,
                                        ignoreBlockers: true,
                                        targetPart: TargetBodyPart.Arms);
            _popup.PopupEntity(Loc.GetString("iv-drip-rip-out-message"), ent, PopupType.MediumCaution);
        }

        ent.Comp.TargetEntity = null;
        ent.Comp.TargetSolutionEntity = null;
        ent.Comp.TargetSolution = null;

        ent.Comp.Connected = false;

        Dirty(ent);
        return true; // Disconnected successfully!
    }
}
