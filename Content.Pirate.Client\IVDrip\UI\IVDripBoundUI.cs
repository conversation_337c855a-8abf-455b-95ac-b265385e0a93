using Content.Goobstation.UIKit.UserInterface.Controls;
using JetBrains.Annotations;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface;

namespace Content.Pirate.Client.IVDrip.UI;

[UsedImplicitly]
public sealed class IVDripBoundUI : BoundUserInterface
{
    private IVDripWindow? _window;

    public IVDripBoundUI(EntityUid owner, Enum uiKey) : base(owner, uiKey)
    {
    }

    protected override void Open()
    {
        base.Open();

        _window = this.CreateWindow<IVDripWindow>();
        _window.OpenCentered();
    }


}
