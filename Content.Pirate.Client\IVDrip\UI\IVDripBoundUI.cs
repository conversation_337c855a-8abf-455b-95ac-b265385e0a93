using Content.Goobstation.UIKit.UserInterface.Controls;
using JetBrains.Annotations;
using Robust.Client.UserInterface.Controls;

namespace Content.Pirate.Client.IVDrip.UI;

[UsedImplicitly]
public sealed class IVDripBoundUI : BoundUserInterface
{
    private IVDripWindow _window;

    public IVDripBoundUI(EntityUid owner, Enum uiKey) : base(owner, uiKey)
    {
        _window = new IVDripWindow();
    }

    protected override void Open()
    {
        base.Open();
    }
}
