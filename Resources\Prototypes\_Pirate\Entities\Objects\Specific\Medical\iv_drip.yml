- type: entity
  name: "IV-ст<PERSON><PERSON>ка"
  id: IVDrip
  parent: BaseStructureDynamic
  description: "Стійка для введення IV-пакетів."
  components:
    - type: DoAfter
    - type: InteractionOutline
    - type: Transform
      noRot: true
    - type: Sprite
      sprite: _Pirate/Objects/Specific/Chemistry/iv_drip.rsi
      layers:
        - state: iv_drip
    - type: Fixtures
      fixtures:
        fix1:
          shape: !type:PhysShapeAabb
            bounds: "-0.15,-0.4,0.15,0.4"
          density: 60
          mask:
            - Impassable
            - TableLayer
            - LowImpassable
          layer:
            - BulletImpassable
            - Opaque
    - type: ItemSlots
      slots:
        beaker:
          name: iv_bag
          whitelist:
            components:
              - SolutionContainerManager
    - type: IVDrip
    - type: ActivatableUI
      key: enum.IVDripUiKey.Key
    - type: UserInterface
      interfaces:
        enum.IVDripUiKey.Key:
          type: IVDripBoundUI
